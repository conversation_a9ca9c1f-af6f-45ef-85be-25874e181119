package com.js.hszpt.service;

import com.js.hszpt.config.ProxyConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Enumeration;

/**
 * HTTP透传服务
 */
@Service
public class HttpProxyService {
    
    private static final Logger logger = LoggerFactory.getLogger(HttpProxyService.class);
    
    @Autowired
    private ProxyConfig proxyConfig;
    
    @Autowired
    @Qualifier("defaultRestTemplate")
    private RestTemplate restTemplate;
    
    /**
     * 处理HTTP请求并转发
     * 
     * @param request 原始HTTP请求
     * @param body 请求体
     * @return 目标服务器的响应
     */
    public ResponseEntity<byte[]> proxyRequest(HttpServletRequest request, byte[] body) {
        try {
            // 构建目标URI
            URI uri = buildTargetUri(request);
            
            // 构建HTTP请求头
            HttpHeaders headers = buildRequestHeaders(request);
            
            // 添加鉴权信息（如果需要）
            if (proxyConfig.isAuthEnabled()) {
                headers.set("Authorization", "Bearer " + proxyConfig.getAuthToken());
                logger.info("已添加鉴权信息");
            }
            
            // 创建HTTP请求实体
            HttpEntity<byte[]> httpEntity = new HttpEntity<>(body, headers);
            
            // 获取HTTP方法
            HttpMethod httpMethod = HttpMethod.valueOf(request.getMethod());
            
            logger.info("转发请求到: {}, 方法: {}", uri, httpMethod);
            
            // 发送请求到目标服务器
            ResponseEntity<byte[]> responseEntity = restTemplate.exchange(
                    uri,
                    httpMethod,
                    httpEntity,
                    byte[].class
            );
            
            logger.info("收到目标服务器响应，状态码: {}", responseEntity.getStatusCode());
            
            return responseEntity;
            
        } catch (Exception e) {
            logger.error("转发请求时发生错误", e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("转发请求时发生错误: " + e.getMessage()).getBytes());
        }
    }
    
    /**
     * 构建目标URI
     */
    private URI buildTargetUri(HttpServletRequest request) throws URISyntaxException {
        String requestUri = request.getRequestURI();
        String queryString = request.getQueryString();
        
        StringBuilder targetUrl = new StringBuilder(proxyConfig.getTargetUrl());
        
        // 如果请求URI以/proxy开头，则移除这部分
        if (requestUri.startsWith("/proxy")) {
            requestUri = requestUri.substring(6); // 移除"/proxy"
        }
        
        // 确保URI以/开头
        if (!requestUri.startsWith("/") && !targetUrl.toString().endsWith("/")) {
            targetUrl.append("/");
        }
        
        targetUrl.append(requestUri);
        
        if (queryString != null && !queryString.isEmpty()) {
            targetUrl.append("?").append(queryString);
        }
        
        return new URI(targetUrl.toString());
    }
    
    /**
     * 构建请求头
     */
    private HttpHeaders buildRequestHeaders(HttpServletRequest request) {
        HttpHeaders headers = new HttpHeaders();
        
        // 复制原始请求的所有请求头
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            
            // 跳过一些特定的请求头，这些请求头可能会导致问题
            if ("host".equalsIgnoreCase(headerName) || 
                "connection".equalsIgnoreCase(headerName) ||
                "content-length".equalsIgnoreCase(headerName)) {
                continue;
            }
            
            Enumeration<String> headerValues = request.getHeaders(headerName);
            while (headerValues.hasMoreElements()) {
                String headerValue = headerValues.nextElement();
                headers.add(headerName, headerValue);
            }
        }
        
        // 设置内容类型（如果原始请求中有）
        String contentType = request.getContentType();
        if (contentType != null) {
            headers.setContentType(MediaType.parseMediaType(contentType));
        }
        
        return headers;
    }
} 